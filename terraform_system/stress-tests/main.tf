terraform {
  backend "s3" {
    bucket  = "wsf-system-infra"
    key     = "terraform-state/stress-tests/terraform.tfstate"
    region  = "eu-west-1"
    encrypt = true
  }
}

module "kubernetes_infra" {
  source = "../common/kubernetes"
  cluster_name = var.k8s_cluster_name
  namespace_name = var.k8s_namespace_name
  environment = var.environment
}
#
# module "kafka_infra" {
#   source = "../common/kafka"
#   cluster_name = var.kafka_cluster_name
#   environment = var.environment
# }
#
# module "redis_infra" {
#   source = "../common/redis"
#   cache_name = var.cache_name
#   environment = var.environment
# }
#
# module "mongo_infra" {
#   source = "../common/mongo"
#   cluster_name = var.mongo_cluster_name
#   environment = var.environment
#   aws_region = var.aws_region
# }
