terraform {
  required_providers {
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = "~> 1.30"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

data "aws_secretsmanager_secret" "secrets" {
  arn = "arn:aws:secretsmanager:eu-west-1:268764888866:secret:terraform_mongoatlas_secrets-tv2dUY"
}

data "aws_secretsmanager_secret_version" "current" {
  secret_id = data.aws_secretsmanager_secret.secrets.id
}

provider "mongodbatlas" {
  public_key  = jsondecode(data.aws_secretsmanager_secret_version.current.secret_string)["mongoatlas_wsftest_public_key"]
  private_key = jsondecode(data.aws_secretsmanager_secret_version.current.secret_string)["mongoatlas_wsftest_private_key"]
}

########################
# MongoDB Atlas Setup  #
########################

data "mongodbatlas_project" "wsf-test" {
  project_id = "637f8a0652e21c1f178374c4"
}

resource "mongodbatlas_flex_cluster" "mongo-cluster" {
  project_id   = data.mongodbatlas_project.wsf-test.id
  name         = var.cluster_name
  provider_settings = {
    backing_provider_name = "AWS"
    region_name           = var.aws_region
  }
  termination_protection_enabled = true
}

resource "mongodbatlas_cloud_provider_access" "aws_access" {
  project_id    = mongodbatlas_project.wsf-test.id
  provider_name = "AWS"
}

output "db_connection_url" {
  value = mongodbatlas_flex_cluster.mongo-cluster.connection_strings.standard_srv
}

###############################
# AWS IAM Role for Atlas User #
###############################

# ⚠️ Replace these with values from MongoDB Atlas IAM DB User UI
locals {
  atlas_account_arn = "arn:aws:iam::************:root"
  atlas_external_id = "EXTERNAL_ID_YOU_SET_IN_ATLAS"   # You choose this in Atlas UI when enabling IAM DB auth
}

data "aws_iam_policy_document" "atlas_trust" {
  statement {
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [local.atlas_account_arn]
    }

    actions = ["sts:AssumeRole"]

    condition {
      test     = "StringEquals"
      variable = "sts:ExternalId"
      values   = [local.atlas_external_id]
    }
  }
}

resource "aws_iam_role" "atlas_db_user_role" {
  name               = "AtlasDBUserRole"
  assume_role_policy = data.aws_iam_policy_document.atlas_trust.json
}

#################################
# Atlas IAM Authenticated User  #
#################################

resource "mongodbatlas_database_user" "iam_user" {
  username           = aws_iam_role.atlas_db_user_role.arn
  project_id         = mongodbatlas_project.wsf-test.id
  auth_database_name = "$external"
  aws_iam_type       = "ROLE"

  roles {
    role_name     = "readWrite"
    database_name = "admin"
  }

  scopes {
    name = mongodbatlas_cluster.wsf-test.name
    type = "CLUSTER"
  }

  depends_on = [aws_iam_role.atlas_db_user_role]
}

# resource "mongodbatlas_cluster" "stress-test-cluster" {
#   project_id   = data.mongodbatlas_project.wsf-test.id
#   name         = "WSF-Stress-Test"
#   provider_name = "AWS"
#   provider_region_name = "EU_WEST_1"
#   provider_instance_size_name = "M10"
#   cloud_backup = false
#   auto_scaling_disk_gb_enabled = false
#   auto_scaling_compute_enabled = false
#
#   tags {
#     key   = "environment"
#     value = "stress-test"
#   }
# }
#
# output "db_connection_url" {
#   value = mongodbatlas_cluster.stress-test-cluster.connection_strings[0].standard_srv
# }